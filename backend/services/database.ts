/**
 * The database service to access and write to the database.
 * Uses TypeORM with SQLite for clean, maintainable database operations.
 * Handles all database operations using TypeORM repositories and entities.
 */

import "reflect-metadata";
import { DataSource, Repository } from "typeorm";
import { ExportJob, ImportJob } from "../entities";
import { LagerstandExportJob } from "../entities/LagerstandExportJob";
import { createModuleLogger } from "../infrastructure/logger";

// Type declaration for Node.js globals
declare const process: any;

const logger = createModuleLogger("database-service");

/**
 * Generate a unique ID for jobs
 */
function generateJobId(): string {
  return (
    Date.now().toString() + "-" + Math.random().toString(36).substring(2, 11)
  );
}

// Re-export entity types for external use
export type { ExportJob, ImportJob, LagerstandExportJob } from "../entities";

/**
 * Database service for managing export and import jobs using TypeORM
 * Provides CRUD operations for all job types with TypeORM repositories
 * Manages SQLite database connection and entity synchronization
 */
export class DatabaseService {
  private static instance: DatabaseService;
  private dataSource: DataSource | null = null;
  private exportJobRepository: Repository<ExportJob> | null = null;
  private importJobRepository: Repository<ImportJob> | null = null;
  private lagerstandExportJobRepository: Repository<LagerstandExportJob> | null =
    null;

  private constructor() {
    this.initialize();
  }

  public static getInstance(): DatabaseService {
    if (!DatabaseService.instance) {
      DatabaseService.instance = new DatabaseService();
    }
    return DatabaseService.instance;
  }

  /**
   * Initialize TypeORM DataSource and repositories
   */
  private async initialize(): Promise<void> {
    try {
      const dbPath = process.env.DATABASE_PATH || "./data/database.sqlite";

      // Create TypeORM DataSource configuration
      this.dataSource = new DataSource({
        type: "better-sqlite3",
        database: dbPath,
        entities: [ExportJob, ImportJob, LagerstandExportJob],
        synchronize: true, // Auto-create tables based on entities
        logging: false, // Set to true for SQL query logging
      });

      // Initialize the connection
      await this.dataSource.initialize();

      // Get repositories
      this.exportJobRepository = this.dataSource.getRepository(ExportJob);
      this.importJobRepository = this.dataSource.getRepository(ImportJob);
      this.lagerstandExportJobRepository =
        this.dataSource.getRepository(LagerstandExportJob);

      logger.info(`TypeORM database connected: ${dbPath}`);
    } catch (error) {
      logger.error("Failed to initialize TypeORM database:", error);
      throw error;
    }
  }

  /**
   * Close the database connection
   */
  public async close(): Promise<void> {
    if (this.dataSource && this.dataSource.isInitialized) {
      await this.dataSource.destroy();
      this.dataSource = null;
      this.exportJobRepository = null;
      this.importJobRepository = null;
      this.lagerstandExportJobRepository = null;
      logger.info("TypeORM database connection closed");
    }
  }
  // ===== EXPORT JOB METHODS =====

  /**
   * Retrieve all export jobs from the database
   * @returns Promise<ExportJob[]> Array of all export jobs
   */
  public async getExportJobs(): Promise<ExportJob[]> {
    try {
      const jobs = await this.exportJobRepository!.find({
        order: { createdAt: "DESC" },
      });

      return jobs;
    } catch (error) {
      logger.error("Failed to get export jobs:", error);
      throw new Error("Failed to retrieve export jobs");
    }
  }

  /**
   * Retrieve a specific export job by ID
   * @param id - The export job ID
   * @returns Promise<ExportJob | null> The export job or null if not found
   */
  public async getExportJob(id: string): Promise<ExportJob | null> {
    try {
      const job = await this.exportJobRepository!.findOne({
        where: { id },
      });

      return job || null;
    } catch (error) {
      logger.error("Failed to get export job:", { id, error });
      throw new Error(`Failed to retrieve export job with ID: ${id}`);
    }
  }

  /**
   * Create a new export job
   * @returns Promise<ExportJob> The created export job
   */
  public async createExportJob(): Promise<ExportJob> {
    try {
      const exportJob = new ExportJob();
      exportJob.id = generateJobId();
      exportJob.startedAt = new Date().toISOString();

      const savedJob = await this.exportJobRepository!.save(exportJob);

      logger.info("Created export job:", { id: savedJob.id });
      return savedJob;
    } catch (error) {
      logger.error("Failed to create export job:", error);
      throw new Error("Failed to create export job");
    }
  }

  /**
   * Update an existing export job
   * @param id - The export job ID
   * @param update - Partial export job data to update
   * @returns Promise<ExportJob> The updated export job
   */
  public async updateExportJob(
    id: string,
    update: Partial<ExportJob>
  ): Promise<ExportJob> {
    try {
      // Find the existing job
      const existingJob = await this.exportJobRepository!.findOne({
        where: { id },
      });

      if (!existingJob) {
        throw new Error(`Export job with ID ${id} not found`);
      }

      // Update the job with provided fields
      Object.assign(existingJob, update);

      // Save the updated job
      const updatedJob = await this.exportJobRepository!.save(existingJob);

      logger.info("Updated export job:", {
        id,
        updateFields: Object.keys(update),
      });
      return updatedJob;
    } catch (error) {
      logger.error("Failed to update export job:", { id, error });
      throw new Error(`Failed to update export job with ID: ${id}`);
    }
  }

  // ===== IMPORT JOB METHODS =====

  /**
   * Retrieve all import jobs from the database
   * @returns Promise<ImportJob[]> Array of all import jobs
   */
  public async getImportJobs(): Promise<ImportJob[]> {
    try {
      const jobs = await this.importJobRepository!.find({
        order: { createdAt: "DESC" },
      });

      return jobs;
    } catch (error) {
      logger.error("Failed to get import jobs:", error);
      throw new Error("Failed to retrieve import jobs");
    }
  }

  /**
   * Retrieve a specific import job by ID
   * @param id - The import job ID
   * @returns Promise<ImportJob | null> The import job or null if not found
   */
  public async getImportJob(id: string): Promise<ImportJob | null> {
    try {
      const job = await this.importJobRepository!.findOne({
        where: { id },
      });

      return job || null;
    } catch (error) {
      logger.error("Failed to get import job:", { id, error });
      throw new Error(`Failed to retrieve import job with ID: ${id}`);
    }
  }

  /**
   * Create a new import job
   * @returns Promise<ImportJob> The created import job
   */
  public async createImportJob(): Promise<ImportJob> {
    try {
      const importJob = new ImportJob();
      importJob.id = generateJobId();
      importJob.startedAt = new Date().toISOString();

      const savedJob = await this.importJobRepository!.save(importJob);

      logger.info("Created import job:", { id: savedJob.id });
      return savedJob;
    } catch (error) {
      logger.error("Failed to create import job:", error);
      throw new Error("Failed to create import job");
    }
  }

  /**
   * Update an existing import job
   * @param id - The import job ID
   * @param update - Partial import job data to update
   * @returns Promise<ImportJob> The updated import job
   */
  public async updateImportJob(
    id: string,
    update: Partial<ImportJob>
  ): Promise<ImportJob> {
    try {
      // Find the existing job
      const existingJob = await this.importJobRepository!.findOne({
        where: { id },
      });

      if (!existingJob) {
        throw new Error(`Import job with ID ${id} not found`);
      }

      // Update the job with provided fields
      Object.assign(existingJob, update);

      // Save the updated job
      const updatedJob = await this.importJobRepository!.save(existingJob);

      logger.info("Updated import job:", {
        id,
        updateFields: Object.keys(update),
      });
      return updatedJob;
    } catch (error) {
      logger.error("Failed to update import job:", { id, error });
      throw new Error(`Failed to update import job with ID: ${id}`);
    }
  }

  // ===== LAGERSTAND EXPORT JOB METHODS =====

  /**
   * Retrieve all lagerstand export jobs from the database
   * @returns Promise<LagerstandExportJob[]> Array of all lagerstand export jobs
   */
  public async getLagerstandExportJobs(): Promise<LagerstandExportJob[]> {
    try {
      const jobs = await this.lagerstandExportJobRepository!.find({
        order: { createdAt: "DESC" },
      });

      return jobs;
    } catch (error) {
      logger.error("Failed to get lagerstand export jobs:", error);
      throw new Error("Failed to retrieve lagerstand export jobs");
    }
  }

  /**
   * Retrieve a specific lagerstand export job by ID
   * @param id - The lagerstand export job ID
   * @returns Promise<LagerstandExportJob | null> The lagerstand export job or null if not found
   */
  public async getLagerstandExportJob(
    id: string
  ): Promise<LagerstandExportJob | null> {
    try {
      const job = await this.lagerstandExportJobRepository!.findOne({
        where: { id },
      });

      return job || null;
    } catch (error) {
      logger.error("Failed to get lagerstand export job:", { id, error });
      throw new Error(
        `Failed to retrieve lagerstand export job with ID: ${id}`
      );
    }
  }

  /**
   * Create a new lagerstand export job
   * @returns Promise<LagerstandExportJob> The created lagerstand export job
   */
  public async createLagerstandExportJob(): Promise<LagerstandExportJob> {
    try {
      const lagerstandExportJob = new LagerstandExportJob();
      lagerstandExportJob.id = generateJobId();
      lagerstandExportJob.startedAt = new Date().toISOString();

      const savedJob = await this.lagerstandExportJobRepository!.save(
        lagerstandExportJob
      );

      logger.info("Created lagerstand export job:", { id: savedJob.id });
      return savedJob;
    } catch (error) {
      logger.error("Failed to create lagerstand export job:", error);
      throw new Error("Failed to create lagerstand export job");
    }
  }

  /**
   * Update an existing lagerstand export job
   * @param id - The lagerstand export job ID
   * @param update - Partial lagerstand export job data to update
   * @returns Promise<LagerstandExportJob> The updated lagerstand export job
   */
  public async updateLagerstandExportJob(
    id: string,
    update: Partial<LagerstandExportJob>
  ): Promise<LagerstandExportJob> {
    try {
      // Find the existing job
      const existingJob = await this.lagerstandExportJobRepository!.findOne({
        where: { id },
      });

      if (!existingJob) {
        throw new Error(`Lagerstand export job with ID ${id} not found`);
      }

      // Update the job with provided fields
      Object.assign(existingJob, update);

      // Save the updated job
      const updatedJob = await this.lagerstandExportJobRepository!.save(
        existingJob
      );

      logger.info("Updated lagerstand export job:", {
        id,
        updateFields: Object.keys(update),
      });
      return updatedJob;
    } catch (error) {
      logger.error("Failed to update lagerstand export job:", { id, error });
      throw new Error(`Failed to update lagerstand export job with ID: ${id}`);
    }
  }
}

// Export singleton instance
export const databaseService = DatabaseService.getInstance();
export default databaseService;
